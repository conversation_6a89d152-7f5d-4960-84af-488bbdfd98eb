import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../presentation/screens/auth/login_screen.dart';
import '../../presentation/screens/auth/register_screen.dart';
import '../../presentation/screens/auth/forgot_password_screen.dart';
import '../../presentation/screens/home/<USER>';
import '../../presentation/screens/wallet/wallet_list_screen.dart';
import '../../presentation/screens/wallet/wallet_detail_screen.dart';
import '../../presentation/screens/wallet/create_wallet_screen.dart';
import '../../presentation/screens/wallet/join_wallet_screen.dart';
import '../../presentation/screens/card/card_list_screen.dart';
import '../../presentation/screens/card/card_detail_screen.dart';
import '../../presentation/screens/payment/payment_screen.dart';

class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: '/login',
    debugLogDiagnostics: true,
    routes: [
      // Authentication Routes
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: '/register',
        name: 'register',
        builder: (context, state) => const RegisterScreen(),
      ),
      GoRoute(
        path: '/forgot-password',
        name: 'forgot-password',
        builder: (context, state) => const ForgotPasswordScreen(),
      ),

      // Main App Routes
      GoRoute(
        path: '/home',
        name: 'home',
        builder: (context, state) => const HomeScreen(),
        routes: [
          // Wallet Routes
          GoRoute(
            path: 'wallets',
            name: 'wallets',
            builder: (context, state) => const WalletListScreen(),
            routes: [
              GoRoute(
                path: 'create',
                name: 'create-wallet',
                builder: (context, state) => const CreateWalletScreen(),
              ),
              GoRoute(
                path: 'join',
                name: 'join-wallet',
                builder: (context, state) {
                  final inviteCode = state.uri.queryParameters['code'];
                  return JoinWalletScreen(inviteCode: inviteCode);
                },
              ),
              GoRoute(
                path: ':walletId',
                name: 'wallet-detail',
                builder: (context, state) {
                  final walletId = state.pathParameters['walletId']!;
                  return WalletDetailScreen(walletId: walletId);
                },
                routes: [
                  GoRoute(
                    path: 'payment',
                    name: 'wallet-payment',
                    builder: (context, state) {
                      final walletId = state.pathParameters['walletId']!;
                      final amount = double.tryParse(
                              state.uri.queryParameters['amount'] ?? '0') ??
                          0.0;
                      final description =
                          state.uri.queryParameters['description'] ??
                              'Wallet contribution';
                      return PaymentScreen(
                        walletId: walletId,
                        amount: amount,
                        description: description,
                      );
                    },
                  ),
                ],
              ),
            ],
          ),

          // Card Routes
          GoRoute(
            path: 'cards',
            name: 'cards',
            builder: (context, state) => const CardListScreen(),
            routes: [
              GoRoute(
                path: ':cardId',
                name: 'card-detail',
                builder: (context, state) {
                  final cardId = state.pathParameters['cardId']!;
                  return CardDetailScreen(cardId: cardId);
                },
              ),
            ],
          ),
        ],
      ),

      // Deep Link Routes
      GoRoute(
        path: '/invite/:code',
        name: 'invite',
        builder: (context, state) {
          final code = state.pathParameters['code']!;
          return JoinWalletScreen(inviteCode: code);
        },
      ),

      GoRoute(
        path: '/wallet/:walletId',
        name: 'wallet-link',
        builder: (context, state) {
          final walletId = state.pathParameters['walletId']!;
          return WalletDetailScreen(walletId: walletId);
        },
      ),

      GoRoute(
        path: '/card/:cardId',
        name: 'card-link',
        builder: (context, state) {
          final cardId = state.pathParameters['cardId']!;
          return CardDetailScreen(cardId: cardId);
        },
      ),
    ],

    // Error handling
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Page not found',
              style: Theme.of(context).textTheme.headlineMedium,
            ),
            const SizedBox(height: 8),
            Text(
              state.error?.toString() ?? 'Unknown error',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go('/home'),
              child: const Text('Go Home'),
            ),
          ],
        ),
      ),
    ),

    // Redirect logic
    redirect: (context, state) {
      // Check if user is authenticated
      // This will be implemented when we have the auth system
      final isAuthenticated = false; // TODO: Get from auth state

      final isAuthRoute = ['/login', '/register', '/forgot-password']
          .contains(state.matchedLocation);

      // If not authenticated and not on auth route, redirect to login
      if (!isAuthenticated && !isAuthRoute) {
        return '/login';
      }

      // If authenticated and on auth route, redirect to home
      if (isAuthenticated && isAuthRoute) {
        return '/home';
      }

      return null;
    },
  );

  // Navigation helpers
  static void goToLogin(BuildContext context) {
    context.go('/login');
  }

  static void goToRegister(BuildContext context) {
    context.go('/register');
  }

  static void goToHome(BuildContext context) {
    context.go('/home');
  }

  static void goToWallets(BuildContext context) {
    context.go('/home/<USER>');
  }

  static void goToWalletDetail(BuildContext context, String walletId) {
    context.go('/home/<USER>/$walletId');
  }

  static void goToCreateWallet(BuildContext context) {
    context.go('/home/<USER>/create');
  }

  static void goToJoinWallet(BuildContext context, {String? inviteCode}) {
    final uri = Uri(
      path: '/home/<USER>/join',
      queryParameters: inviteCode != null ? {'code': inviteCode} : null,
    );
    context.go(uri.toString());
  }

  static void goToCards(BuildContext context) {
    context.go('/home/<USER>');
  }

  static void goToCardDetail(BuildContext context, String cardId) {
    context.go('/home/<USER>/$cardId');
  }

  static void goToPayment(BuildContext context, String walletId) {
    context.go('/home/<USER>/$walletId/payment');
  }
}
