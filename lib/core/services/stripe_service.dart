import 'package:flutter_stripe/flutter_stripe.dart' as stripe;
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import '../config/app_config.dart';
import '../errors/exceptions.dart';
import '../../domain/entities/payment.dart';
import '../../data/models/payment_model.dart';

class StripeService {
  final Dio _dio;
  late final String _publishableKey;
  late final String _secretKey;

  StripeService(this._dio) {
    _publishableKey = AppConfig.stripePublishableKey;
    _secretKey = AppConfig.stripeSecretKey;

    // Initialize Stripe
    stripe.Stripe.publishableKey = _publishableKey;
  }

  // Initialize Stripe SDK
  Future<void> initialize() async {
    try {
      await stripe.Stripe.instance.applySettings();
    } catch (e) {
      throw PaymentException(
        message: 'Failed to initialize Stripe: ${e.toString()}',
        code: 500,
      );
    }
  }

  // Create Payment Intent
  Future<PaymentIntent> createPaymentIntent(
      CreatePaymentIntentRequest request) async {
    try {
      final response = await _dio.post(
        'https://api.stripe.com/v1/payment_intents',
        options: Options(
          headers: {
            'Authorization': 'Bearer $_secretKey',
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        ),
        data: {
          'amount': request.amount,
          'currency': request.currency,
          if (request.customerId != null) 'customer': request.customerId,
          if (request.paymentMethodId != null)
            'payment_method': request.paymentMethodId,
          if (request.description != null) 'description': request.description,
          if (request.metadata != null)
            ...request.metadata!.map((k, v) => MapEntry('metadata[$k]', v)),
          if (request.confirmImmediately) 'confirm': 'true',
          'automatic_payment_methods[enabled]': 'true',
        },
      );

      return PaymentIntentModel.fromJson(response.data).toEntity();
    } on DioException catch (e) {
      throw PaymentException(
        message: 'Failed to create payment intent: ${e.message}',
        code: e.response?.statusCode ?? 500,
      );
    } catch (e) {
      throw PaymentException(
        message: 'Unexpected error creating payment intent: ${e.toString()}',
        code: 500,
      );
    }
  }

  // Confirm Payment Intent
  Future<PaymentIntent> confirmPaymentIntent(
      String paymentIntentId, String paymentMethodId) async {
    try {
      final response = await _dio.post(
        'https://api.stripe.com/v1/payment_intents/$paymentIntentId/confirm',
        options: Options(
          headers: {
            'Authorization': 'Bearer $_secretKey',
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        ),
        data: {
          'payment_method': paymentMethodId,
        },
      );

      return PaymentIntentModel.fromJson(response.data).toEntity();
    } on DioException catch (e) {
      throw PaymentException(
        message: 'Failed to confirm payment intent: ${e.message}',
        code: e.response?.statusCode ?? 500,
      );
    } catch (e) {
      throw PaymentException(
        message: 'Unexpected error confirming payment intent: ${e.toString()}',
        code: 500,
      );
    }
  }

  // Get Payment Intent
  Future<PaymentIntent> getPaymentIntent(String paymentIntentId) async {
    try {
      final response = await _dio.get(
        'https://api.stripe.com/v1/payment_intents/$paymentIntentId',
        options: Options(
          headers: {
            'Authorization': 'Bearer $_secretKey',
          },
        ),
      );

      return PaymentIntentModel.fromJson(response.data).toEntity();
    } on DioException catch (e) {
      throw PaymentException(
        message: 'Failed to get payment intent: ${e.message}',
        code: e.response?.statusCode ?? 500,
      );
    } catch (e) {
      throw PaymentException(
        message: 'Unexpected error getting payment intent: ${e.toString()}',
        code: 500,
      );
    }
  }

  // Create Customer
  Future<StripeCustomer> createCustomer(CreateCustomerRequest request) async {
    try {
      final response = await _dio.post(
        'https://api.stripe.com/v1/customers',
        options: Options(
          headers: {
            'Authorization': 'Bearer $_secretKey',
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        ),
        data: {
          'email': request.email,
          if (request.name != null) 'name': request.name,
          if (request.phone != null) 'phone': request.phone,
          if (request.description != null) 'description': request.description,
          if (request.metadata != null)
            ...request.metadata!.map((k, v) => MapEntry('metadata[$k]', v)),
        },
      );

      return StripeCustomerModel.fromJson(response.data).toEntity();
    } on DioException catch (e) {
      throw PaymentException(
        message: 'Failed to create customer: ${e.message}',
        code: e.response?.statusCode ?? 500,
      );
    } catch (e) {
      throw PaymentException(
        message: 'Unexpected error creating customer: ${e.toString()}',
        code: 500,
      );
    }
  }

  // Get Customer
  Future<StripeCustomer> getCustomer(String customerId) async {
    try {
      final response = await _dio.get(
        'https://api.stripe.com/v1/customers/$customerId',
        options: Options(
          headers: {
            'Authorization': 'Bearer $_secretKey',
          },
        ),
      );

      return StripeCustomerModel.fromJson(response.data).toEntity();
    } on DioException catch (e) {
      throw PaymentException(
        message: 'Failed to get customer: ${e.message}',
        code: e.response?.statusCode ?? 500,
      );
    } catch (e) {
      throw PaymentException(
        message: 'Unexpected error getting customer: ${e.toString()}',
        code: 500,
      );
    }
  }

  // List Customer Payment Methods
  Future<List<PaymentMethod>> getCustomerPaymentMethods(
      String customerId) async {
    try {
      final response = await _dio.get(
        'https://api.stripe.com/v1/payment_methods',
        queryParameters: {
          'customer': customerId,
          'type': 'card',
        },
        options: Options(
          headers: {
            'Authorization': 'Bearer $_secretKey',
          },
        ),
      );

      final data = response.data['data'] as List;
      return data
          .map((json) => PaymentMethodModel.fromJson(json).toEntity())
          .toList();
    } on DioException catch (e) {
      throw PaymentException(
        message: 'Failed to get payment methods: ${e.message}',
        code: e.response?.statusCode ?? 500,
      );
    } catch (e) {
      throw PaymentException(
        message: 'Unexpected error getting payment methods: ${e.toString()}',
        code: 500,
      );
    }
  }

  // Present Payment Sheet
  Future<void> presentPaymentSheet() async {
    try {
      await stripe.Stripe.instance.presentPaymentSheet();
    } on stripe.StripeException catch (e) {
      throw PaymentException(
        message: 'Payment sheet error: ${e.error.localizedMessage}',
        code: 400,
      );
    } catch (e) {
      throw PaymentException(
        message: 'Unexpected payment sheet error: ${e.toString()}',
        code: 500,
      );
    }
  }

  // Initialize Payment Sheet
  Future<void> initPaymentSheet({
    required String paymentIntentClientSecret,
    String? customerId,
    String? customerEphemeralKeySecret,
    String? merchantDisplayName,
  }) async {
    try {
      await stripe.Stripe.instance.initPaymentSheet(
        paymentSheetParameters: stripe.SetupPaymentSheetParameters(
          paymentIntentClientSecret: paymentIntentClientSecret,
          customerId: customerId,
          customerEphemeralKeySecret: customerEphemeralKeySecret,
          merchantDisplayName: merchantDisplayName ?? 'Potto',
          style: ThemeMode.system,
          appearance: const stripe.PaymentSheetAppearance(
            primaryButton: stripe.PaymentSheetPrimaryButtonAppearance(
              colors: stripe.PaymentSheetPrimaryButtonTheme(
                light: stripe.PaymentSheetPrimaryButtonThemeColors(
                  background: Color(0xFF6366F1), // Indigo
                  text: Color(0xFFFFFFFF),
                ),
                dark: stripe.PaymentSheetPrimaryButtonThemeColors(
                  background: Color(0xFF6366F1), // Indigo
                  text: Color(0xFFFFFFFF),
                ),
              ),
            ),
          ),
        ),
      );
    } on stripe.StripeException catch (e) {
      throw PaymentException(
        message:
            'Payment sheet initialization error: ${e.error.localizedMessage}',
        code: 400,
      );
    } catch (e) {
      throw PaymentException(
        message:
            'Unexpected payment sheet initialization error: ${e.toString()}',
        code: 500,
      );
    }
  }

  // Verify webhook signature
  bool verifyWebhookSignature(
      String payload, String signature, String endpointSecret) {
    try {
      // This is a simplified version - in production, use proper webhook verification
      return signature.isNotEmpty && endpointSecret.isNotEmpty;
    } catch (e) {
      return false;
    }
  }
}
