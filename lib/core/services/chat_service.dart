import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:uuid/uuid.dart';
import '../errors/exceptions.dart' as app_exceptions;

/// Service for managing group chat functionality
class ChatService {
  final SupabaseClient _supabaseClient;
  final Uuid _uuid;

  ChatService(this._supabaseClient, this._uuid);

  /// Send a message to a wallet's group chat
  Future<ChatMessage> sendMessage({
    required String walletId,
    required String content,
    String? replyToId,
    ChatMessageType type = ChatMessageType.text,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final user = _supabaseClient.auth.currentUser;
      if (user == null) {
        throw app_exceptions.AuthException(message: 'User not authenticated');
      }

      // Verify user is a member of the wallet
      final memberCheck = await _supabaseClient
          .from('wallet_members')
          .select('id')
          .eq('wallet_id', walletId)
          .eq('user_id', user.id)
          .maybeSingle();

      if (memberCheck == null) {
        throw app_exceptions.AuthException(
            message: 'User is not a member of this wallet');
      }

      final messageId = _uuid.v4();
      final now = DateTime.now();

      final messageData = {
        'id': messageId,
        'wallet_id': walletId,
        'user_id': user.id,
        'content': content,
        'type': type.name,
        'reply_to_id': replyToId,
        'metadata': metadata,
        'created_at': now.toIso8601String(),
        'updated_at': now.toIso8601String(),
      };

      final result =
          await _supabaseClient.from('messages').insert(messageData).select('''
            *,
            user:users(id, email, full_name, avatar_url),
            reply_to:messages(id, content, user:users(email, full_name))
          ''').single();

      return ChatMessage.fromJson(result);
    } on PostgrestException catch (e) {
      throw app_exceptions.ServerException(message: e.message);
    } catch (e) {
      throw app_exceptions.ServerException(
          message: 'Failed to send message: ${e.toString()}');
    }
  }

  /// Get messages for a wallet's group chat
  Future<List<ChatMessage>> getMessages({
    required String walletId,
    int limit = 50,
    String? before,
    String? after,
  }) async {
    try {
      final user = _supabaseClient.auth.currentUser;
      if (user == null) {
        throw app_exceptions.AuthException(message: 'User not authenticated');
      }

      // Verify user is a member of the wallet
      final memberCheck = await _supabaseClient
          .from('wallet_members')
          .select('id')
          .eq('wallet_id', walletId)
          .eq('user_id', user.id)
          .maybeSingle();

      if (memberCheck == null) {
        throw app_exceptions.AuthException(
            message: 'User is not a member of this wallet');
      }

      var query = _supabaseClient
          .from('messages')
          .select('''
            *,
            user:users(id, email, full_name, avatar_url),
            reply_to:messages(id, content, user:users(email, full_name))
          ''')
          .eq('wallet_id', walletId)
          .order('created_at', ascending: false)
          .limit(limit);

      // TODO: Add date filtering when Supabase client methods are available
      // if (before != null) {
      //   query = query.lte('created_at', before);
      // }
      // if (after != null) {
      //   query = query.gte('created_at', after);
      // }

      final result = await query;
      return result.map((json) => ChatMessage.fromJson(json)).toList();
    } on PostgrestException catch (e) {
      throw app_exceptions.ServerException(message: e.message);
    } catch (e) {
      throw app_exceptions.ServerException(
          message: 'Failed to get messages: ${e.toString()}');
    }
  }

  /// Update a message (only by the sender)
  Future<ChatMessage> updateMessage({
    required String messageId,
    required String content,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final user = _supabaseClient.auth.currentUser;
      if (user == null) {
        throw app_exceptions.AuthException(message: 'User not authenticated');
      }

      final updateData = {
        'content': content,
        'metadata': metadata,
        'updated_at': DateTime.now().toIso8601String(),
        'is_edited': true,
      };

      final result = await _supabaseClient
          .from('messages')
          .update(updateData)
          .eq('id', messageId)
          .eq('user_id',
              user.id) // Only allow user to update their own messages
          .select('''
            *,
            user:users(id, email, full_name, avatar_url),
            reply_to:messages(id, content, user:users(email, full_name))
          ''').single();

      return ChatMessage.fromJson(result);
    } on PostgrestException catch (e) {
      throw app_exceptions.ServerException(message: e.message);
    } catch (e) {
      throw app_exceptions.ServerException(
          message: 'Failed to update message: ${e.toString()}');
    }
  }

  /// Delete a message (only by the sender)
  Future<void> deleteMessage(String messageId) async {
    try {
      final user = _supabaseClient.auth.currentUser;
      if (user == null) {
        throw app_exceptions.AuthException(message: 'User not authenticated');
      }

      await _supabaseClient.from('messages').delete().eq('id', messageId).eq(
          'user_id', user.id); // Only allow user to delete their own messages
    } on PostgrestException catch (e) {
      throw app_exceptions.ServerException(message: e.message);
    } catch (e) {
      throw app_exceptions.ServerException(
          message: 'Failed to delete message: ${e.toString()}');
    }
  }

  /// Mark messages as read
  Future<void> markMessagesAsRead({
    required String walletId,
    required List<String> messageIds,
  }) async {
    try {
      final user = _supabaseClient.auth.currentUser;
      if (user == null) {
        throw app_exceptions.AuthException(message: 'User not authenticated');
      }

      // Insert or update read receipts
      final readReceipts = messageIds
          .map((messageId) => {
                'message_id': messageId,
                'user_id': user.id,
                'read_at': DateTime.now().toIso8601String(),
              })
          .toList();

      await _supabaseClient.from('message_read_receipts').upsert(readReceipts);
    } on PostgrestException catch (e) {
      throw app_exceptions.ServerException(message: e.message);
    } catch (e) {
      throw app_exceptions.ServerException(
          message: 'Failed to mark messages as read: ${e.toString()}');
    }
  }

  /// Get unread message count for a wallet
  Future<int> getUnreadCount(String walletId) async {
    try {
      final user = _supabaseClient.auth.currentUser;
      if (user == null) {
        throw app_exceptions.AuthException(message: 'User not authenticated');
      }

      final result =
          await _supabaseClient.rpc('get_unread_message_count', params: {
        'wallet_id_param': walletId,
        'user_id_param': user.id,
      });

      return result as int? ?? 0;
    } on PostgrestException catch (e) {
      throw app_exceptions.ServerException(message: e.message);
    } catch (e) {
      throw app_exceptions.ServerException(
          message: 'Failed to get unread count: ${e.toString()}');
    }
  }

  /// React to a message
  Future<void> reactToMessage({
    required String messageId,
    required String emoji,
  }) async {
    try {
      final user = _supabaseClient.auth.currentUser;
      if (user == null) {
        throw app_exceptions.AuthException(message: 'User not authenticated');
      }

      await _supabaseClient.from('message_reactions').upsert({
        'message_id': messageId,
        'user_id': user.id,
        'emoji': emoji,
        'created_at': DateTime.now().toIso8601String(),
      });
    } on PostgrestException catch (e) {
      throw app_exceptions.ServerException(message: e.message);
    } catch (e) {
      throw app_exceptions.ServerException(
          message: 'Failed to react to message: ${e.toString()}');
    }
  }

  /// Remove reaction from a message
  Future<void> removeReaction({
    required String messageId,
    required String emoji,
  }) async {
    try {
      final user = _supabaseClient.auth.currentUser;
      if (user == null) {
        throw app_exceptions.AuthException(message: 'User not authenticated');
      }

      await _supabaseClient
          .from('message_reactions')
          .delete()
          .eq('message_id', messageId)
          .eq('user_id', user.id)
          .eq('emoji', emoji);
    } on PostgrestException catch (e) {
      throw app_exceptions.ServerException(message: e.message);
    } catch (e) {
      throw app_exceptions.ServerException(
          message: 'Failed to remove reaction: ${e.toString()}');
    }
  }
}

/// Enum for message types
enum ChatMessageType {
  text,
  image,
  file,
  system,
  transaction,
}

/// Chat message model
class ChatMessage {
  final String id;
  final String walletId;
  final String userId;
  final String content;
  final ChatMessageType type;
  final String? replyToId;
  final Map<String, dynamic>? metadata;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isEdited;
  final ChatUser? user;
  final ChatMessage? replyTo;
  final List<MessageReaction> reactions;

  ChatMessage({
    required this.id,
    required this.walletId,
    required this.userId,
    required this.content,
    required this.type,
    this.replyToId,
    this.metadata,
    required this.createdAt,
    required this.updatedAt,
    this.isEdited = false,
    this.user,
    this.replyTo,
    this.reactions = const [],
  });

  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      id: json['id'],
      walletId: json['wallet_id'],
      userId: json['user_id'],
      content: json['content'],
      type: ChatMessageType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => ChatMessageType.text,
      ),
      replyToId: json['reply_to_id'],
      metadata: json['metadata'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      isEdited: json['is_edited'] ?? false,
      user: json['user'] != null ? ChatUser.fromJson(json['user']) : null,
      replyTo: json['reply_to'] != null
          ? ChatMessage.fromJson(json['reply_to'])
          : null,
      reactions: json['reactions'] != null
          ? (json['reactions'] as List)
              .map((r) => MessageReaction.fromJson(r))
              .toList()
          : [],
    );
  }
}

/// Chat user model
class ChatUser {
  final String id;
  final String email;
  final String? fullName;
  final String? avatarUrl;

  ChatUser({
    required this.id,
    required this.email,
    this.fullName,
    this.avatarUrl,
  });

  factory ChatUser.fromJson(Map<String, dynamic> json) {
    return ChatUser(
      id: json['id'],
      email: json['email'],
      fullName: json['full_name'],
      avatarUrl: json['avatar_url'],
    );
  }

  String get displayName => fullName ?? email.split('@').first;
}

/// Message reaction model
class MessageReaction {
  final String messageId;
  final String userId;
  final String emoji;
  final DateTime createdAt;

  MessageReaction({
    required this.messageId,
    required this.userId,
    required this.emoji,
    required this.createdAt,
  });

  factory MessageReaction.fromJson(Map<String, dynamic> json) {
    return MessageReaction(
      messageId: json['message_id'],
      userId: json['user_id'],
      emoji: json['emoji'],
      createdAt: DateTime.parse(json['created_at']),
    );
  }
}
