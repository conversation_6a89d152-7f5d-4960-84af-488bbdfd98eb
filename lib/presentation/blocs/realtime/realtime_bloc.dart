import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/services/realtime_service.dart' as service;
import '../../../core/services/chat_service.dart';

import 'realtime_event.dart';
import 'realtime_state.dart';

class RealtimeBloc extends Bloc<RealtimeEvent, RealtimeState> {
  final service.RealtimeService _realtimeService;
  final ChatService _chatService;

  final Map<String, StreamSubscription> _subscriptions = {};

  RealtimeBloc({
    required service.RealtimeService realtimeService,
    required ChatService chatService,
  })  : _realtimeService = realtimeService,
        _chatService = chatService,
        super(const RealtimeInitial()) {
    on<InitializeRealtime>(_onInitializeRealtime);
    on<SubscribeToWallet>(_onSubscribeToWallet);
    on<UnsubscribeFromWallet>(_onUnsubscribeFromWallet);
    on<SubscribeToNotifications>(_onSubscribeToNotifications);
    on<SubscribeToCards>(_onSubscribeToCards);
    on<SubscribeToChat>(_onSubscribeToChat);
    on<UnsubscribeFromChat>(_onUnsubscribeFromChat);
    on<SendChatMessage>(_onSendChatMessage);
    on<LoadChatMessages>(_onLoadChatMessages);
    on<UpdateChatMessage>(_onUpdateChatMessage);
    on<DeleteChatMessage>(_onDeleteChatMessage);
    on<ReactToMessage>(_onReactToMessage);
    on<RemoveMessageReaction>(_onRemoveMessageReaction);
    on<MarkMessagesAsRead>(_onMarkMessagesAsRead);
    on<LoadUnreadCount>(_onLoadUnreadCount);
    on<CleanupRealtime>(_onCleanupRealtime);
    on<HandleWalletRealtimeEvent>(_onHandleWalletRealtimeEvent);
    on<HandleNotificationRealtimeEvent>(_onHandleNotificationRealtimeEvent);
    on<HandleCardRealtimeEvent>(_onHandleCardRealtimeEvent);
    on<HandleMessageRealtimeEvent>(_onHandleMessageRealtimeEvent);
  }

  Future<void> _onInitializeRealtime(
    InitializeRealtime event,
    Emitter<RealtimeState> emit,
  ) async {
    try {
      emit(const RealtimeLoading());

      emit(RealtimeInitialized(userId: event.userId));
    } catch (e) {
      emit(RealtimeError(message: e.toString()));
    }
  }

  Future<void> _onSubscribeToWallet(
    SubscribeToWallet event,
    Emitter<RealtimeState> emit,
  ) async {
    try {
      final currentState = state;
      if (currentState is! RealtimeInitialized) return;

      // Cancel existing subscription if any
      await _subscriptions['wallet_${event.walletId}']?.cancel();

      // Subscribe to wallet updates
      final subscription = _realtimeService
          .subscribeToWallet(event.walletId)
          .listen((walletEvent) {
        add(
          HandleWalletRealtimeEvent({
            'type': walletEvent.type.name,
            'wallet_id': walletEvent.walletId,
            'data': walletEvent.data,
            'timestamp': walletEvent.timestamp.toIso8601String(),
          }),
        );
      });

      _subscriptions['wallet_${event.walletId}'] = subscription;

      emit(
        currentState.copyWith(
          subscribedWallets: {
            ...currentState.subscribedWallets,
            event.walletId,
          },
        ),
      );
    } catch (e) {
      emit(
        RealtimeError(
          message: 'Failed to subscribe to wallet: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onUnsubscribeFromWallet(
    UnsubscribeFromWallet event,
    Emitter<RealtimeState> emit,
  ) async {
    try {
      final currentState = state;
      if (currentState is! RealtimeInitialized) return;

      await _subscriptions['wallet_${event.walletId}']?.cancel();
      _subscriptions.remove('wallet_${event.walletId}');

      _realtimeService.unsubscribe('wallet_${event.walletId}');

      final updatedWallets = Set<String>.from(currentState.subscribedWallets);
      updatedWallets.remove(event.walletId);

      emit(currentState.copyWith(subscribedWallets: updatedWallets));
    } catch (e) {
      emit(
        RealtimeError(
          message: 'Failed to unsubscribe from wallet: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onSubscribeToNotifications(
    SubscribeToNotifications event,
    Emitter<RealtimeState> emit,
  ) async {
    try {
      final currentState = state;
      if (currentState is! RealtimeInitialized) return;

      // Cancel existing subscription if any
      await _subscriptions['notifications_${event.userId}']?.cancel();

      // Subscribe to notifications
      final subscription = _realtimeService
          .subscribeToNotifications(event.userId)
          .listen((notificationEvent) {
        add(
          HandleNotificationRealtimeEvent({
            'type': notificationEvent.type.name,
            'data': notificationEvent.data,
            'timestamp': notificationEvent.timestamp.toIso8601String(),
          }),
        );
      });

      _subscriptions['notifications_${event.userId}'] = subscription;

      emit(currentState.copyWith(notificationsSubscribed: true));
    } catch (e) {
      emit(
        RealtimeError(
          message: 'Failed to subscribe to notifications: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onSubscribeToCards(
    SubscribeToCards event,
    Emitter<RealtimeState> emit,
  ) async {
    try {
      final currentState = state;
      if (currentState is! RealtimeInitialized) return;

      // Cancel existing subscription if any
      await _subscriptions['cards_${event.userId}']?.cancel();

      // Subscribe to card updates
      final subscription =
          _realtimeService.subscribeToCards(event.userId).listen((cardEvent) {
        add(
          HandleCardRealtimeEvent({
            'type': cardEvent.type.name,
            'card_id': cardEvent.cardId,
            'data': cardEvent.data,
            'timestamp': cardEvent.timestamp.toIso8601String(),
          }),
        );
      });

      _subscriptions['cards_${event.userId}'] = subscription;

      emit(currentState.copyWith(cardsSubscribed: true));
    } catch (e) {
      emit(
        RealtimeError(message: 'Failed to subscribe to cards: ${e.toString()}'),
      );
    }
  }

  Future<void> _onSubscribeToChat(
    SubscribeToChat event,
    Emitter<RealtimeState> emit,
  ) async {
    try {
      final currentState = state;
      if (currentState is! RealtimeInitialized) return;

      // Cancel existing subscription if any
      await _subscriptions['chat_${event.walletId}']?.cancel();

      // Subscribe to chat messages
      final subscription = _realtimeService
          .subscribeToChat(event.walletId)
          .listen((messageEvent) {
        add(
          HandleMessageRealtimeEvent({
            'type': messageEvent.type.name,
            'wallet_id': messageEvent.walletId,
            'data': messageEvent.data,
            'timestamp': messageEvent.timestamp.toIso8601String(),
          }),
        );
      });

      _subscriptions['chat_${event.walletId}'] = subscription;

      final updatedChats = Set<String>.from(currentState.subscribedChats);
      updatedChats.add(event.walletId);

      emit(currentState.copyWith(subscribedChats: updatedChats));
    } catch (e) {
      emit(
        RealtimeError(message: 'Failed to subscribe to chat: ${e.toString()}'),
      );
    }
  }

  Future<void> _onUnsubscribeFromChat(
    UnsubscribeFromChat event,
    Emitter<RealtimeState> emit,
  ) async {
    try {
      final currentState = state;
      if (currentState is! RealtimeInitialized) return;

      await _subscriptions['chat_${event.walletId}']?.cancel();
      _subscriptions.remove('chat_${event.walletId}');

      _realtimeService.unsubscribe('chat_${event.walletId}');

      final updatedChats = Set<String>.from(currentState.subscribedChats);
      updatedChats.remove(event.walletId);

      emit(currentState.copyWith(subscribedChats: updatedChats));
    } catch (e) {
      emit(
        RealtimeError(
          message: 'Failed to unsubscribe from chat: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onSendChatMessage(
    SendChatMessage event,
    Emitter<RealtimeState> emit,
  ) async {
    try {
      final message = await _chatService.sendMessage(
        walletId: event.walletId,
        content: event.content,
        replyToId: event.replyToId,
        type: event.type,
        metadata: event.metadata,
      );

      emit(ChatMessageSent(message));
    } catch (e) {
      emit(RealtimeError(message: 'Failed to send message: ${e.toString()}'));
    }
  }

  Future<void> _onLoadChatMessages(
    LoadChatMessages event,
    Emitter<RealtimeState> emit,
  ) async {
    try {
      final messages = await _chatService.getMessages(
        walletId: event.walletId,
        limit: event.limit,
        before: event.before,
        after: event.after,
      );

      final unreadCount = await _chatService.getUnreadCount(event.walletId);

      emit(
        ChatMessagesLoaded(
          walletId: event.walletId,
          messages: messages,
          hasMore: messages.length == event.limit,
          unreadCount: unreadCount,
        ),
      );
    } catch (e) {
      emit(RealtimeError(message: 'Failed to load messages: ${e.toString()}'));
    }
  }

  Future<void> _onUpdateChatMessage(
    UpdateChatMessage event,
    Emitter<RealtimeState> emit,
  ) async {
    try {
      final message = await _chatService.updateMessage(
        messageId: event.messageId,
        content: event.content,
        metadata: event.metadata,
      );

      emit(ChatMessageUpdated(message));
    } catch (e) {
      emit(RealtimeError(message: 'Failed to update message: ${e.toString()}'));
    }
  }

  Future<void> _onDeleteChatMessage(
    DeleteChatMessage event,
    Emitter<RealtimeState> emit,
  ) async {
    try {
      await _chatService.deleteMessage(event.messageId);
      emit(ChatMessageDeleted(event.messageId));
    } catch (e) {
      emit(RealtimeError(message: 'Failed to delete message: ${e.toString()}'));
    }
  }

  Future<void> _onReactToMessage(
    ReactToMessage event,
    Emitter<RealtimeState> emit,
  ) async {
    try {
      await _chatService.reactToMessage(
        messageId: event.messageId,
        emoji: event.emoji,
      );

      emit(
        MessageReactionAdded(messageId: event.messageId, emoji: event.emoji),
      );
    } catch (e) {
      emit(
        RealtimeError(message: 'Failed to react to message: ${e.toString()}'),
      );
    }
  }

  Future<void> _onRemoveMessageReaction(
    RemoveMessageReaction event,
    Emitter<RealtimeState> emit,
  ) async {
    try {
      await _chatService.removeReaction(
        messageId: event.messageId,
        emoji: event.emoji,
      );

      emit(
        MessageReactionRemoved(messageId: event.messageId, emoji: event.emoji),
      );
    } catch (e) {
      emit(
        RealtimeError(message: 'Failed to remove reaction: ${e.toString()}'),
      );
    }
  }

  Future<void> _onMarkMessagesAsRead(
    MarkMessagesAsRead event,
    Emitter<RealtimeState> emit,
  ) async {
    try {
      await _chatService.markMessagesAsRead(
        walletId: event.walletId,
        messageIds: event.messageIds,
      );

      emit(
        MessagesMarkedAsRead(
          walletId: event.walletId,
          messageIds: event.messageIds,
        ),
      );
    } catch (e) {
      emit(
        RealtimeError(
          message: 'Failed to mark messages as read: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onLoadUnreadCount(
    LoadUnreadCount event,
    Emitter<RealtimeState> emit,
  ) async {
    try {
      final count = await _chatService.getUnreadCount(event.walletId);
      emit(UnreadCountUpdated(walletId: event.walletId, count: count));
    } catch (e) {
      emit(
        RealtimeError(message: 'Failed to load unread count: ${e.toString()}'),
      );
    }
  }

  Future<void> _onCleanupRealtime(
    CleanupRealtime event,
    Emitter<RealtimeState> emit,
  ) async {
    try {
      // Cancel all subscriptions
      for (final subscription in _subscriptions.values) {
        await subscription.cancel();
      }
      _subscriptions.clear();

      // Unsubscribe from all channels
      _realtimeService.unsubscribeAll();

      emit(const RealtimeInitial());
    } catch (e) {
      emit(
        RealtimeError(message: 'Failed to cleanup realtime: ${e.toString()}'),
      );
    }
  }

  Future<void> _onHandleWalletRealtimeEvent(
    HandleWalletRealtimeEvent event,
    Emitter<RealtimeState> emit,
  ) async {
    try {
      final walletEvent = service.WalletRealtimeEvent(
        type: service.RealtimeEventType.values.firstWhere(
          (e) => e.name == event.eventData['type'],
          orElse: () => service.RealtimeEventType.update,
        ),
        walletId: event.eventData['wallet_id'],
        data: event.eventData['data'] ?? {},
        timestamp: DateTime.parse(event.eventData['timestamp']),
      );

      emit(WalletRealtimeEventReceived(walletEvent));
    } catch (e) {
      emit(
        RealtimeError(
          message: 'Failed to handle wallet event: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onHandleNotificationRealtimeEvent(
    HandleNotificationRealtimeEvent event,
    Emitter<RealtimeState> emit,
  ) async {
    try {
      final notificationEvent = service.NotificationRealtimeEvent(
        type: service.RealtimeEventType.values.firstWhere(
          (e) => e.name == event.eventData['type'],
          orElse: () => service.RealtimeEventType.insert,
        ),
        data: event.eventData['data'] ?? {},
        timestamp: DateTime.parse(event.eventData['timestamp']),
      );

      emit(NotificationRealtimeEventReceived(notificationEvent));
    } catch (e) {
      emit(
        RealtimeError(
          message: 'Failed to handle notification event: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onHandleCardRealtimeEvent(
    HandleCardRealtimeEvent event,
    Emitter<RealtimeState> emit,
  ) async {
    try {
      final cardEvent = service.CardRealtimeEvent(
        type: service.RealtimeEventType.values.firstWhere(
          (e) => e.name == event.eventData['type'],
          orElse: () => service.RealtimeEventType.update,
        ),
        cardId: event.eventData['card_id'],
        data: event.eventData['data'] ?? {},
        timestamp: DateTime.parse(event.eventData['timestamp']),
      );

      emit(CardRealtimeEventReceived(cardEvent));
    } catch (e) {
      emit(
        RealtimeError(message: 'Failed to handle card event: ${e.toString()}'),
      );
    }
  }

  Future<void> _onHandleMessageRealtimeEvent(
    HandleMessageRealtimeEvent event,
    Emitter<RealtimeState> emit,
  ) async {
    try {
      final messageData = event.eventData['data'] as Map<String, dynamic>;
      final message = ChatMessage.fromJson(messageData);

      emit(
        NewChatMessageReceived(
          walletId: event.eventData['wallet_id'],
          message: message,
        ),
      );
    } catch (e) {
      emit(
        RealtimeError(
          message: 'Failed to handle message event: ${e.toString()}',
        ),
      );
    }
  }

  @override
  Future<void> close() async {
    // Cancel all subscriptions before closing
    for (final subscription in _subscriptions.values) {
      await subscription.cancel();
    }
    _subscriptions.clear();

    // Cleanup realtime service
    _realtimeService.unsubscribeAll();

    return super.close();
  }
}
