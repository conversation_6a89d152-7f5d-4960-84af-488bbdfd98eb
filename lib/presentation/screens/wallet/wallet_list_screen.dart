import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../domain/entities/wallet.dart';
import '../../blocs/wallet/wallet_bloc.dart';
import '../../blocs/wallet/wallet_event.dart';
import '../../blocs/wallet/wallet_state.dart';
import '../../widgets/common/custom_card.dart';
import '../../widgets/common/custom_button.dart';
import '../../widgets/common/empty_state.dart';
import '../../widgets/common/app_bar.dart';

class WalletListScreen extends StatefulWidget {
  const WalletListScreen({super.key});

  @override
  State<WalletListScreen> createState() => _WalletListScreenState();
}

class _WalletListScreenState extends State<WalletListScreen> {
  @override
  void initState() {
    super.initState();
    context.read<WalletBloc>().add(LoadWallets());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'My Wallets',
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              // TODO: Implement search functionality
            },
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'create':
                  context.push('/create-wallet');
                  break;
                case 'join':
                  context.push('/join-wallet');
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'create',
                child: Row(
                  children: [
                    Icon(Icons.add),
                    SizedBox(width: 8),
                    Text('Create Wallet'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'join',
                child: Row(
                  children: [
                    Icon(Icons.group_add),
                    SizedBox(width: 8),
                    Text('Join Wallet'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: BlocConsumer<WalletBloc, WalletState>(
        listener: (context, state) {
          if (state is WalletError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is WalletLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state is WalletsLoaded) {
            if (state.wallets.isEmpty) {
              return _buildEmptyState();
            }
            return _buildWalletList(state.wallets);
          }

          return _buildEmptyState();
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return EmptyState(
      icon: Icons.account_balance_wallet_outlined,
      title: 'No Wallets Yet',
      subtitle:
          'Create your first wallet or join an existing one to start pooling money with friends',
      customAction: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 32),
        child: Column(
          children: [
            CustomButton(
              text: 'Create Wallet',
              icon: const Icon(Icons.add, size: 20),
              variant: ButtonVariant.primary,
              isFullWidth: true,
              onPressed: () => context.push('/create-wallet'),
            ),
            const SizedBox(height: 12),
            CustomButton(
              text: 'Join Wallet',
              icon: const Icon(Icons.group_add, size: 20),
              variant: ButtonVariant.outline,
              isFullWidth: true,
              onPressed: () => context.push('/join-wallet'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWalletList(List<Wallet> wallets) {
    return RefreshIndicator(
      onRefresh: () async {
        context.read<WalletBloc>().add(LoadWallets());
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: wallets.length,
        itemBuilder: (context, index) {
          final wallet = wallets[index];
          return _buildWalletCard(wallet);
        },
      ),
    );
  }

  Widget _buildWalletCard(Wallet wallet) {
    final progress =
        wallet.goalAmount > 0 ? wallet.currentAmount / wallet.goalAmount : 0.0;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => context.push('/wallet/${wallet.id}'),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    backgroundColor: _getWalletColor(wallet.type),
                    child: Icon(
                      _getWalletIcon(wallet.type),
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          wallet.name,
                          style: Theme.of(context)
                              .textTheme
                              .titleMedium
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        if (wallet.description?.isNotEmpty == true)
                          Text(
                            wallet.description!,
                            style: Theme.of(context)
                                .textTheme
                                .bodySmall
                                ?.copyWith(color: Colors.grey[600]),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                      ],
                    ),
                  ),
                  _buildStatusChip(wallet.status),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Current Amount',
                          style: Theme.of(context)
                              .textTheme
                              .bodySmall
                              ?.copyWith(color: Colors.grey[600]),
                        ),
                        Text(
                          '${wallet.currency} ${wallet.currentAmount.toStringAsFixed(2)}',
                          style: Theme.of(
                            context,
                          ).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Colors.green[700],
                              ),
                        ),
                      ],
                    ),
                  ),
                  if (wallet.goalAmount > 0)
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            'Goal: ${wallet.currency} ${wallet.goalAmount.toStringAsFixed(2)}',
                            style: Theme.of(context)
                                .textTheme
                                .bodySmall
                                ?.copyWith(color: Colors.grey[600]),
                          ),
                          const SizedBox(height: 4),
                          LinearProgressIndicator(
                            value: progress.clamp(0.0, 1.0),
                            backgroundColor: Colors.grey[300],
                            valueColor: AlwaysStoppedAnimation<Color>(
                              progress >= 1.0 ? Colors.green : Colors.blue,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '${(progress * 100).toStringAsFixed(1)}%',
                            style: Theme.of(context)
                                .textTheme
                                .bodySmall
                                ?.copyWith(fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
              if (wallet.members.isNotEmpty) ...[
                const SizedBox(height: 12),
                Row(
                  children: [
                    Icon(Icons.people, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      '${wallet.members.length} member${wallet.members.length != 1 ? 's' : ''}',
                      style: Theme.of(
                        context,
                      ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                    ),
                    const Spacer(),
                    if (wallet.deadline != null)
                      Text(
                        'Due: ${_formatDate(wallet.deadline!)}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: _isDeadlineNear(wallet.deadline!)
                                  ? Colors.red[600]
                                  : Colors.grey[600],
                            ),
                      ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(WalletStatus status) {
    Color color;
    String label;

    switch (status) {
      case WalletStatus.active:
        color = Colors.green;
        label = 'Active';
        break;
      case WalletStatus.locked:
        color = Colors.orange;
        label = 'Locked';
        break;
      case WalletStatus.closed:
        color = Colors.grey;
        label = 'Closed';
        break;
    }

    return Chip(
      label: Text(
        label,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
      backgroundColor: color.withOpacity(0.1),
      side: BorderSide(color: color.withOpacity(0.3)),
    );
  }

  Color _getWalletColor(WalletType type) {
    switch (type) {
      case WalletType.personal:
        return Colors.blue;
      case WalletType.group:
        return Colors.purple;
      case WalletType.savings:
        return Colors.green;
      case WalletType.project:
        return Colors.orange;
      case WalletType.travel:
        return Colors.teal;
      case WalletType.event:
        return Colors.indigo;
      case WalletType.gift:
        return Colors.pink;
      case WalletType.general:
        return Colors.grey;
    }
  }

  IconData _getWalletIcon(WalletType type) {
    switch (type) {
      case WalletType.personal:
        return Icons.person;
      case WalletType.group:
        return Icons.group;
      case WalletType.savings:
        return Icons.savings;
      case WalletType.project:
        return Icons.work;
      case WalletType.travel:
        return Icons.flight;
      case WalletType.event:
        return Icons.event;
      case WalletType.gift:
        return Icons.card_giftcard;
      case WalletType.general:
        return Icons.account_balance_wallet;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = date.difference(now).inDays;

    if (difference < 0) {
      return 'Overdue';
    } else if (difference == 0) {
      return 'Today';
    } else if (difference == 1) {
      return 'Tomorrow';
    } else if (difference < 7) {
      return '${difference} days';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  bool _isDeadlineNear(DateTime deadline) {
    final now = DateTime.now();
    final difference = deadline.difference(now).inDays;
    return difference <= 7 && difference >= 0;
  }
}
