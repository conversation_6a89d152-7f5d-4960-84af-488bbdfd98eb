import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../domain/entities/wallet.dart';
import '../../../domain/usecases/wallet/create_wallet_usecase.dart';
import '../../blocs/wallet/wallet_bloc.dart';
import '../../blocs/wallet/wallet_event.dart';
import '../../blocs/wallet/wallet_state.dart';
import '../../widgets/common/custom_button.dart';
import '../../widgets/common/custom_text_field.dart';
import '../../widgets/common/custom_card.dart';
import '../../widgets/common/app_bar.dart';

class CreateWalletScreen extends StatefulWidget {
  const CreateWalletScreen({super.key});

  @override
  State<CreateWalletScreen> createState() => _CreateWalletScreenState();
}

class _CreateWalletScreenState extends State<CreateWalletScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _goalAmountController = TextEditingController();

  WalletType _selectedType = WalletType.personal;
  String _selectedCurrency = 'USD';
  DateTime? _selectedDeadline;

  final List<String> _currencies = ['USD', 'EUR', 'GBP', 'CAD', 'AUD'];

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _goalAmountController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Create Wallet',
        actions: [
          CustomButton(
            text: 'Create',
            variant: ButtonVariant.ghost,
            size: ButtonSize.small,
            onPressed: _isFormValid() ? _createWallet : null,
          ),
        ],
      ),
      body: BlocListener<WalletBloc, WalletState>(
        listener: (context, state) {
          if (state is WalletCreated) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Wallet created successfully!'),
                backgroundColor: Colors.green,
              ),
            );
            context.pop();
          } else if (state is WalletError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildWalletTypeSection(),
                const SizedBox(height: 24),
                _buildBasicInfoSection(),
                const SizedBox(height: 24),
                _buildGoalSection(),
                const SizedBox(height: 24),
                _buildDeadlineSection(),
                const SizedBox(height: 32),
                _buildCreateButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildWalletTypeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Wallet Type',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          children: WalletType.values.map((type) {
            return ChoiceChip(
              label: Text(_getWalletTypeLabel(type)),
              selected: _selectedType == type,
              onSelected: (selected) {
                if (selected) {
                  setState(() {
                    _selectedType = type;
                  });
                }
              },
              avatar: Icon(_getWalletTypeIcon(type), size: 18),
            );
          }).toList(),
        ),
        const SizedBox(height: 8),
        Text(
          _getWalletTypeDescription(_selectedType),
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
        ),
      ],
    );
  }

  Widget _buildBasicInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Basic Information',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _nameController,
          decoration: const InputDecoration(
            labelText: 'Wallet Name',
            hintText: 'Enter a name for your wallet',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.account_balance_wallet),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Please enter a wallet name';
            }
            if (value.trim().length < 3) {
              return 'Wallet name must be at least 3 characters';
            }
            return null;
          },
          onChanged: (_) => setState(() {}),
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _descriptionController,
          decoration: const InputDecoration(
            labelText: 'Description (Optional)',
            hintText: 'What is this wallet for?',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.description),
          ),
          maxLines: 3,
          maxLength: 200,
        ),
        const SizedBox(height: 16),
        DropdownButtonFormField<String>(
          value: _selectedCurrency,
          decoration: const InputDecoration(
            labelText: 'Currency',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.attach_money),
          ),
          items: _currencies.map((currency) {
            return DropdownMenuItem(value: currency, child: Text(currency));
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedCurrency = value;
              });
            }
          },
        ),
      ],
    );
  }

  Widget _buildGoalSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Goal Amount (Optional)',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _goalAmountController,
          decoration: InputDecoration(
            labelText: 'Goal Amount',
            hintText: 'Enter target amount',
            border: const OutlineInputBorder(),
            prefixIcon: const Icon(Icons.flag),
            suffixText: _selectedCurrency,
          ),
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          validator: (value) {
            if (value != null && value.isNotEmpty) {
              final amount = double.tryParse(value);
              if (amount == null || amount <= 0) {
                return 'Please enter a valid amount';
              }
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildDeadlineSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Deadline (Optional)',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        InkWell(
          onTap: _selectDeadline,
          child: InputDecorator(
            decoration: const InputDecoration(
              labelText: 'Deadline',
              hintText: 'Select a deadline',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.calendar_today),
            ),
            child: Text(
              _selectedDeadline != null
                  ? '${_selectedDeadline!.day}/${_selectedDeadline!.month}/${_selectedDeadline!.year}'
                  : 'No deadline set',
              style: TextStyle(
                color: _selectedDeadline != null
                    ? Theme.of(context).textTheme.bodyLarge?.color
                    : Theme.of(context).hintColor,
              ),
            ),
          ),
        ),
        if (_selectedDeadline != null) ...[
          const SizedBox(height: 8),
          Row(
            children: [
              const Spacer(),
              TextButton(
                onPressed: () {
                  setState(() {
                    _selectedDeadline = null;
                  });
                },
                child: const Text('Clear'),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildCreateButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isFormValid() ? _createWallet : null,
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
        child: const Text('Create Wallet'),
      ),
    );
  }

  bool _isFormValid() {
    return _nameController.text.trim().isNotEmpty &&
        _nameController.text.trim().length >= 3;
  }

  Future<void> _selectDeadline() async {
    final now = DateTime.now();
    final picked = await showDatePicker(
      context: context,
      initialDate: _selectedDeadline ?? now.add(const Duration(days: 30)),
      firstDate: now,
      lastDate: now.add(const Duration(days: 365 * 2)),
    );

    if (picked != null) {
      setState(() {
        _selectedDeadline = picked;
      });
    }
  }

  void _createWallet() {
    if (!_formKey.currentState!.validate()) return;

    final goalAmount = _goalAmountController.text.isNotEmpty
        ? double.tryParse(_goalAmountController.text) ?? 0.0
        : 0.0;

    final request = CreateWalletRequest(
      name: _nameController.text.trim(),
      description: _descriptionController.text.trim(),
      type: _selectedType,
      goalAmount: goalAmount,
      currency: _selectedCurrency,
      deadline: _selectedDeadline,
      imageUrl: null,
      settings: null,
    );

    context.read<WalletBloc>().add(CreateWallet(request));
  }

  String _getWalletTypeLabel(WalletType type) {
    switch (type) {
      case WalletType.personal:
        return 'Personal';
      case WalletType.group:
        return 'Group';
      case WalletType.savings:
        return 'Savings';
      case WalletType.project:
        return 'Project';
      case WalletType.travel:
        return 'Travel';
      case WalletType.event:
        return 'Event';
      case WalletType.gift:
        return 'Gift';
      case WalletType.general:
        return 'General';
    }
  }

  IconData _getWalletTypeIcon(WalletType type) {
    switch (type) {
      case WalletType.personal:
        return Icons.person;
      case WalletType.group:
        return Icons.group;
      case WalletType.savings:
        return Icons.savings;
      case WalletType.project:
        return Icons.work;
      case WalletType.travel:
        return Icons.flight;
      case WalletType.event:
        return Icons.event;
      case WalletType.gift:
        return Icons.card_giftcard;
      case WalletType.general:
        return Icons.account_balance_wallet;
    }
  }

  String _getWalletTypeDescription(WalletType type) {
    switch (type) {
      case WalletType.personal:
        return 'A wallet for your personal expenses and savings';
      case WalletType.group:
        return 'A shared wallet for group expenses and contributions';
      case WalletType.savings:
        return 'A dedicated wallet for saving towards a specific goal';
      case WalletType.project:
        return 'A wallet for project-related expenses and funding';
      case WalletType.travel:
        return 'A wallet for travel expenses and trip planning';
      case WalletType.event:
        return 'A wallet for event planning and related expenses';
      case WalletType.gift:
        return 'A wallet for gift purchases and special occasions';
      case WalletType.general:
        return 'A general-purpose wallet for various expenses';
    }
  }
}
