import '../../core/services/stripe_service.dart';
import '../../core/errors/exceptions.dart';
import '../../domain/entities/payment.dart';
import '../models/payment_model.dart';

abstract class PaymentRemoteDataSource {
  Future<PaymentIntent> createPaymentIntent(CreatePaymentIntentRequest request);
  Future<PaymentIntent> confirmPaymentIntent(
      String paymentIntentId, String paymentMethodId);
  Future<PaymentIntent> getPaymentIntent(String paymentIntentId);
  Future<StripeCustomer> createCustomer(CreateCustomerRequest request);
  Future<StripeCustomer> getCustomer(String customerId);
  Future<StripeCustomer> updateCustomer(
      String customerId, CreateCustomerRequest request);
  Future<List<PaymentMethod>> getCustomerPaymentMethods(String customerId);
  Future<PaymentMethod> attachPaymentMethodToCustomer(
      String paymentMethodId, String customerId);
  Future<void> detachPaymentMethodFromCustomer(String paymentMethodId);
  Future<void> initPaymentSheet({
    required String paymentIntentClientSecret,
    String? customerId,
    String? customerEphemeralKeySecret,
    String? merchantDisplayName,
  });
  Future<void> presentPaymentSheet();
  Future<WebhookEvent> processWebhookEvent(String payload, String signature);
}

class PaymentRemoteDataSourceImpl implements PaymentRemoteDataSource {
  final StripeService _stripeService;

  PaymentRemoteDataSourceImpl(this._stripeService);

  @override
  Future<PaymentIntent> createPaymentIntent(
      CreatePaymentIntentRequest request) async {
    try {
      return await _stripeService.createPaymentIntent(request);
    } catch (e) {
      throw PaymentException(
        message: 'Failed to create payment intent: ${e.toString()}',
        code: 500,
      );
    }
  }

  @override
  Future<PaymentIntent> confirmPaymentIntent(
      String paymentIntentId, String paymentMethodId) async {
    try {
      return await _stripeService.confirmPaymentIntent(
          paymentIntentId, paymentMethodId);
    } catch (e) {
      throw PaymentException(
        message: 'Failed to confirm payment intent: ${e.toString()}',
        code: 500,
      );
    }
  }

  @override
  Future<PaymentIntent> getPaymentIntent(String paymentIntentId) async {
    try {
      return await _stripeService.getPaymentIntent(paymentIntentId);
    } catch (e) {
      throw PaymentException(
        message: 'Failed to get payment intent: ${e.toString()}',
        code: 500,
      );
    }
  }

  @override
  Future<StripeCustomer> createCustomer(CreateCustomerRequest request) async {
    try {
      return await _stripeService.createCustomer(request);
    } catch (e) {
      throw PaymentException(
        message: 'Failed to create customer: ${e.toString()}',
        code: 500,
      );
    }
  }

  @override
  Future<StripeCustomer> getCustomer(String customerId) async {
    try {
      return await _stripeService.getCustomer(customerId);
    } catch (e) {
      throw PaymentException(
        message: 'Failed to get customer: ${e.toString()}',
        code: 500,
      );
    }
  }

  @override
  Future<StripeCustomer> updateCustomer(
      String customerId, CreateCustomerRequest request) async {
    try {
      // For simplicity, we'll create a new customer - in production, implement proper update
      return await _stripeService.createCustomer(request);
    } catch (e) {
      throw PaymentException(
        message: 'Failed to update customer: ${e.toString()}',
        code: 500,
      );
    }
  }

  @override
  Future<List<PaymentMethod>> getCustomerPaymentMethods(
      String customerId) async {
    try {
      return await _stripeService.getCustomerPaymentMethods(customerId);
    } catch (e) {
      throw PaymentException(
        message: 'Failed to get payment methods: ${e.toString()}',
        code: 500,
      );
    }
  }

  @override
  Future<PaymentMethod> attachPaymentMethodToCustomer(
      String paymentMethodId, String customerId) async {
    try {
      // This would typically involve a Stripe API call to attach the payment method
      // For now, return a mock payment method
      return PaymentMethod(
        id: 'pm_mock',
        type: PaymentMethodType.card,
        createdAt: DateTime.now(),
      );
    } catch (e) {
      throw PaymentException(
        message: 'Failed to attach payment method: ${e.toString()}',
        code: 500,
      );
    }
  }

  @override
  Future<void> detachPaymentMethodFromCustomer(String paymentMethodId) async {
    try {
      // This would typically involve a Stripe API call to detach the payment method
      // For now, just return
      return;
    } catch (e) {
      throw PaymentException(
        message: 'Failed to detach payment method: ${e.toString()}',
        code: 500,
      );
    }
  }

  @override
  Future<void> initPaymentSheet({
    required String paymentIntentClientSecret,
    String? customerId,
    String? customerEphemeralKeySecret,
    String? merchantDisplayName,
  }) async {
    try {
      await _stripeService.initPaymentSheet(
        paymentIntentClientSecret: paymentIntentClientSecret,
        customerId: customerId,
        customerEphemeralKeySecret: customerEphemeralKeySecret,
        merchantDisplayName: merchantDisplayName,
      );
    } catch (e) {
      throw PaymentException(
        message: 'Failed to initialize payment sheet: ${e.toString()}',
        code: 500,
      );
    }
  }

  @override
  Future<void> presentPaymentSheet() async {
    try {
      await _stripeService.presentPaymentSheet();
    } catch (e) {
      throw PaymentException(
        message: 'Failed to present payment sheet: ${e.toString()}',
        code: 500,
      );
    }
  }

  @override
  Future<WebhookEvent> processWebhookEvent(
      String payload, String signature) async {
    try {
      // Parse the webhook payload
      // This is a simplified implementation - in production, properly verify and parse
      final event = WebhookEvent(
        id: 'evt_mock',
        type: 'payment_intent.succeeded',
        data: {'object': 'payment_intent'},
        createdAt: DateTime.now(),
        livemode: false,
      );

      return event;
    } catch (e) {
      throw PaymentException(
        message: 'Failed to process webhook event: ${e.toString()}',
        code: 500,
      );
    }
  }
}
