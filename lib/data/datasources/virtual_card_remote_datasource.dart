import 'package:supabase_flutter/supabase_flutter.dart';
import '../../core/errors/exceptions.dart';
import '../../core/services/stripe_issuing_service.dart';
import '../../domain/entities/virtual_card.dart';
import '../models/virtual_card_model.dart';

abstract class VirtualCardRemoteDataSource {
  Future<VirtualCardModel> createVirtualCard(CreateVirtualCardRequest request);
  Future<VirtualCardModel> getVirtualCard(String cardId);
  Future<List<VirtualCardModel>> getVirtualCardsByWallet(String walletId);
  Future<List<VirtualCardModel>> getVirtualCardsByUser(String userId);
  Future<VirtualCardModel> updateVirtualCard(UpdateVirtualCardRequest request);
  Future<void> deleteVirtualCard(String cardId);
  Future<VirtualCardModel> activateCard(String cardId);
  Future<VirtualCardModel> deactivateCard(String cardId);
  Future<VirtualCardModel> cancelCard(String cardId);
  Future<SpendingControlModel> createSpendingControl(
      CreateSpendingControlRequest request);
  Future<List<SpendingControlModel>> getSpendingControls(String cardId);
  Future<SpendingControlModel> updateSpendingControl(
      String controlId, CreateSpendingControlRequest request);
  Future<void> deleteSpendingControl(String controlId);
  Future<Map<String, dynamic>> getCardDetails(String cardId);
  Future<String> getCardPin(String cardId);
  Future<void> updateCardPin(String cardId, String newPin);
  Future<bool> authorizeTransaction({
    required String cardId,
    required double amount,
    required String currency,
    required String merchantName,
    required String merchantCategory,
    Map<String, dynamic>? metadata,
  });
}

class VirtualCardRemoteDataSourceImpl implements VirtualCardRemoteDataSource {
  final SupabaseClient supabaseClient;
  final StripeIssuingService stripeIssuingService;

  VirtualCardRemoteDataSourceImpl({
    required this.supabaseClient,
    required this.stripeIssuingService,
  });

  @override
  Future<VirtualCardModel> createVirtualCard(
      CreateVirtualCardRequest request) async {
    try {
      // First, get user details for cardholder creation
      final userResponse = await supabaseClient
          .from('users')
          .select('email, phone')
          .eq('id', request.userId)
          .single();

      // Create cardholder in Stripe
      final cardholderResponse = await stripeIssuingService.createCardholder(
        name: request.cardholderName,
        email: userResponse['email'],
        phoneNumber: userResponse['phone'] ?? '+1234567890',
        metadata: {
          'user_id': request.userId,
          'wallet_id': request.walletId,
          ...?request.metadata,
        },
      );

      // Create card in Stripe
      final cardResponse = await stripeIssuingService.createCard(
        cardholderId: cardholderResponse['id'],
        currency: 'usd',
        type: request.type,
        metadata: {
          'user_id': request.userId,
          'wallet_id': request.walletId,
          ...?request.metadata,
        },
      );

      // Create virtual card model from Stripe response
      final cardModel = VirtualCardModel.fromStripeResponse(
        cardResponse,
        walletId: request.walletId,
        userId: request.userId,
      );

      // Save to Supabase
      final dbResponse = await supabaseClient
          .from('virtual_cards')
          .insert(cardModel.toJson())
          .select()
          .single();

      // Save spending controls if provided
      if (request.spendingControls.isNotEmpty) {
        final controlsData = request.spendingControls.map((control) {
          return SpendingControlModel.fromEntity(control.copyWith(
            cardId: cardModel.id,
            id: DateTime.now().millisecondsSinceEpoch.toString(),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          )).toJson();
        }).toList();

        await supabaseClient.from('spending_controls').insert(controlsData);
      }

      return VirtualCardModel.fromJson(dbResponse);
    } on PostgrestException catch (e) {
      throw DatabaseException(
          message: e.message, code: int.tryParse(e.code ?? ''));
    } catch (e) {
      throw CardException(
          message: 'Failed to create virtual card: ${e.toString()}');
    }
  }

  @override
  Future<VirtualCardModel> getVirtualCard(String cardId) async {
    try {
      final response = await supabaseClient
          .from('virtual_cards')
          .select('*, spending_controls(*)')
          .eq('id', cardId)
          .single();

      return VirtualCardModel.fromJson(response);
    } on PostgrestException catch (e) {
      throw DatabaseException(
          message: e.message, code: int.tryParse(e.code ?? ''));
    } catch (e) {
      throw CardException(
          message: 'Failed to get virtual card: ${e.toString()}');
    }
  }

  @override
  Future<List<VirtualCardModel>> getVirtualCardsByWallet(
      String walletId) async {
    try {
      final response = await supabaseClient
          .from('virtual_cards')
          .select('*, spending_controls(*)')
          .eq('wallet_id', walletId)
          .order('created_at', ascending: false);

      return response.map((json) => VirtualCardModel.fromJson(json)).toList();
    } on PostgrestException catch (e) {
      throw DatabaseException(
          message: e.message, code: int.tryParse(e.code ?? ''));
    } catch (e) {
      throw CardException(
          message: 'Failed to get virtual cards: ${e.toString()}');
    }
  }

  @override
  Future<List<VirtualCardModel>> getVirtualCardsByUser(String userId) async {
    try {
      final response = await supabaseClient
          .from('virtual_cards')
          .select('*, spending_controls(*)')
          .eq('user_id', userId)
          .order('created_at', ascending: false);

      return response.map((json) => VirtualCardModel.fromJson(json)).toList();
    } on PostgrestException catch (e) {
      throw DatabaseException(
          message: e.message, code: int.tryParse(e.code ?? ''));
    } catch (e) {
      throw CardException(
          message: 'Failed to get virtual cards: ${e.toString()}');
    }
  }

  @override
  Future<VirtualCardModel> updateVirtualCard(
      UpdateVirtualCardRequest request) async {
    try {
      final updateData = <String, dynamic>{
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (request.status != null) {
        updateData['status'] = request.status!.name;

        // Update status in Stripe as well
        if (request.status != null) {
          final card = await getVirtualCard(request.cardId);
          if (card.stripeCardId != null) {
            await stripeIssuingService.updateCard(
              cardId: card.stripeCardId!,
              status: request.status!.name,
            );
          }
        }
      }

      if (request.metadata != null) {
        updateData['metadata'] = request.metadata;
      }

      final response = await supabaseClient
          .from('virtual_cards')
          .update(updateData)
          .eq('id', request.cardId)
          .select('*, spending_controls(*)')
          .single();

      return VirtualCardModel.fromJson(response);
    } on PostgrestException catch (e) {
      throw DatabaseException(
          message: e.message, code: int.tryParse(e.code ?? ''));
    } catch (e) {
      throw CardException(
          message: 'Failed to update virtual card: ${e.toString()}');
    }
  }

  @override
  Future<void> deleteVirtualCard(String cardId) async {
    try {
      // First cancel the card in Stripe
      final card = await getVirtualCard(cardId);
      if (card.stripeCardId != null) {
        await stripeIssuingService.updateCard(
          cardId: card.stripeCardId!,
          status: 'canceled',
        );
      }

      // Delete from database
      await supabaseClient.from('virtual_cards').delete().eq('id', cardId);
    } on PostgrestException catch (e) {
      throw DatabaseException(
          message: e.message, code: int.tryParse(e.code ?? ''));
    } catch (e) {
      throw CardException(
          message: 'Failed to delete virtual card: ${e.toString()}');
    }
  }

  @override
  Future<VirtualCardModel> activateCard(String cardId) async {
    return updateVirtualCard(UpdateVirtualCardRequest(
      cardId: cardId,
      status: VirtualCardStatus.active,
    ));
  }

  @override
  Future<VirtualCardModel> deactivateCard(String cardId) async {
    return updateVirtualCard(UpdateVirtualCardRequest(
      cardId: cardId,
      status: VirtualCardStatus.inactive,
    ));
  }

  @override
  Future<VirtualCardModel> cancelCard(String cardId) async {
    return updateVirtualCard(UpdateVirtualCardRequest(
      cardId: cardId,
      status: VirtualCardStatus.canceled,
    ));
  }

  @override
  Future<SpendingControlModel> createSpendingControl(
      CreateSpendingControlRequest request) async {
    try {
      final controlModel = SpendingControlModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        cardId: request.cardId,
        type: request.type,
        amount: request.amount,
        interval: request.interval,
        categories: request.categories,
        allowedMerchants: request.allowedMerchants,
        blockedMerchants: request.blockedMerchants,
        isActive: true,
        validFrom: request.validFrom,
        validUntil: request.validUntil,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final response = await supabaseClient
          .from('spending_controls')
          .insert(controlModel.toJson())
          .select()
          .single();

      return SpendingControlModel.fromJson(response);
    } on PostgrestException catch (e) {
      throw DatabaseException(
          message: e.message, code: int.tryParse(e.code ?? ''));
    } catch (e) {
      throw CardException(
          message: 'Failed to create spending control: ${e.toString()}');
    }
  }

  @override
  Future<List<SpendingControlModel>> getSpendingControls(String cardId) async {
    try {
      final response = await supabaseClient
          .from('spending_controls')
          .select()
          .eq('card_id', cardId)
          .order('created_at', ascending: false);

      return response
          .map((json) => SpendingControlModel.fromJson(json))
          .toList();
    } on PostgrestException catch (e) {
      throw DatabaseException(
          message: e.message, code: int.tryParse(e.code ?? ''));
    } catch (e) {
      throw CardException(
          message: 'Failed to get spending controls: ${e.toString()}');
    }
  }

  @override
  Future<SpendingControlModel> updateSpendingControl(
      String controlId, CreateSpendingControlRequest request) async {
    try {
      final updateData = {
        'type': request.type.name,
        'amount': request.amount,
        'interval': request.interval?.name,
        'categories': request.categories,
        'allowed_merchants': request.allowedMerchants,
        'blocked_merchants': request.blockedMerchants,
        'valid_from': request.validFrom?.toIso8601String(),
        'valid_until': request.validUntil?.toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      final response = await supabaseClient
          .from('spending_controls')
          .update(updateData)
          .eq('id', controlId)
          .select()
          .single();

      return SpendingControlModel.fromJson(response);
    } on PostgrestException catch (e) {
      throw DatabaseException(
          message: e.message, code: int.tryParse(e.code ?? ''));
    } catch (e) {
      throw CardException(
          message: 'Failed to update spending control: ${e.toString()}');
    }
  }

  @override
  Future<void> deleteSpendingControl(String controlId) async {
    try {
      await supabaseClient
          .from('spending_controls')
          .delete()
          .eq('id', controlId);
    } on PostgrestException catch (e) {
      throw DatabaseException(
          message: e.message, code: int.tryParse(e.code ?? ''));
    } catch (e) {
      throw CardException(
          message: 'Failed to delete spending control: ${e.toString()}');
    }
  }

  @override
  Future<Map<String, dynamic>> getCardDetails(String cardId) async {
    try {
      final card = await getVirtualCard(cardId);
      if (card.stripeCardId == null) {
        throw CardException(message: 'Card not found in Stripe');
      }

      return await stripeIssuingService.getCardDetails(card.stripeCardId!);
    } catch (e) {
      throw CardException(
          message: 'Failed to get card details: ${e.toString()}');
    }
  }

  @override
  Future<String> getCardPin(String cardId) async {
    // This would typically be handled through a secure endpoint
    // For now, return a placeholder
    throw UnimplementedError(
        'PIN retrieval not implemented for security reasons');
  }

  @override
  Future<void> updateCardPin(String cardId, String newPin) async {
    // This would typically be handled through a secure endpoint
    // For now, throw unimplemented
    throw UnimplementedError('PIN update not implemented for security reasons');
  }

  @override
  Future<bool> authorizeTransaction({
    required String cardId,
    required double amount,
    required String currency,
    required String merchantName,
    required String merchantCategory,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      // Get card and check spending controls
      final card = await getVirtualCard(cardId);

      // Check if card is active
      if (!card.isActive) return false;

      // Check spending limits
      for (final control in card.spendingControls) {
        if (!control.isCurrentlyValid) continue;

        if (control.type == SpendingControlType.amount &&
            control.amount != null) {
          if (amount > control.amount!) return false;
        }

        if (control.type == SpendingControlType.merchant) {
          if (control.blockedMerchants?.contains(merchantName) == true)
            return false;
          if (control.allowedMerchants != null &&
              !control.allowedMerchants!.contains(merchantName)) return false;
        }

        if (control.type == SpendingControlType.category) {
          if (control.categories != null &&
              !control.categories!.contains(merchantCategory)) return false;
        }
      }

      return true;
    } catch (e) {
      return false;
    }
  }
}
