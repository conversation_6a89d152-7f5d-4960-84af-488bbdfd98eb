import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:uuid/uuid.dart';
import '../../core/errors/exceptions.dart' as app_exceptions;
import '../../domain/entities/wallet.dart';
import '../../domain/entities/invitation.dart';
import '../../domain/usecases/wallet/create_wallet_usecase.dart';
import '../../domain/usecases/wallet/invitation_usecase.dart';
import '../../domain/usecases/wallet/member_management_usecase.dart';
import '../models/wallet_model.dart';
import '../models/invitation_model.dart';
import 'wallet_remote_datasource.dart';

class WalletRemoteDataSourceImpl implements WalletRemoteDataSource {
  final SupabaseClient supabaseClient;
  final Uuid uuid;

  WalletRemoteDataSourceImpl({
    required this.supabaseClient,
    required this.uuid,
  });

  @override
  Future<WalletModel> createWallet(CreateWalletRequest request) async {
    try {
      final user = supabaseClient.auth.currentUser;
      if (user == null) {
        throw app_exceptions.AuthException(message: 'User not authenticated');
      }

      final walletId = uuid.v4();
      final now = DateTime.now();

      // Create wallet
      final walletData = {
        'id': walletId,
        'name': request.name,
        'description': request.description,
        'type': request.type.name,
        'status': WalletStatus.active.name,
        'goal_amount': request.goalAmount,
        'current_amount': 0.0,
        'currency': request.currency,
        'deadline': request.deadline?.toIso8601String(),
        'created_by': user.id,
        'created_at': now.toIso8601String(),
        'updated_at': now.toIso8601String(),
        'image_url': request.imageUrl,
      };

      final walletResponse = await supabaseClient
          .from('wallets')
          .insert(walletData)
          .select()
          .single();

      // Create default wallet settings
      final settingsData = {
        'wallet_id': walletId,
        'require_approval_for_spending': false,
        'approval_threshold': 0.0,
        'allow_member_invites': true,
        'allow_member_spending': true,
        'notify_on_transactions': true,
        'notify_on_contributions': true,
        'notify_on_invitations': true,
        'auto_lock_on_goal_reached': false,
        'auto_lock_on_deadline': false,
      };

      await supabaseClient.from('wallet_settings').insert(settingsData);

      // Add creator as admin member
      final memberData = {
        'user_id': user.id,
        'wallet_id': walletId,
        'role': MemberRole.admin.name,
        'contributed_amount': 0.0,
        'spending_limit': 0.0,
        'can_spend': true,
        'can_invite': true,
        'joined_at': now.toIso8601String(),
      };

      await supabaseClient.from('wallet_members').insert(memberData);

      // Fetch the complete wallet with members and settings
      return await getWallet(walletId);
    } on PostgrestException catch (e) {
      throw app_exceptions.DatabaseException(
          message: e.message, code: int.tryParse(e.code ?? ''));
    } catch (e) {
      throw app_exceptions.ServerException(
        message: 'Failed to create wallet: ${e.toString()}',
      );
    }
  }

  @override
  Future<WalletModel> getWallet(String walletId) async {
    try {
      final response = await supabaseClient.from('wallets').select('''
            *,
            wallet_members(*),
            wallet_settings(*)
          ''').eq('id', walletId).single();

      return WalletModel.fromJson(response);
    } on PostgrestException catch (e) {
      throw app_exceptions.DatabaseException(
          message: e.message, code: int.tryParse(e.code ?? ''));
    } catch (e) {
      throw app_exceptions.ServerException(
          message: 'Failed to get wallet: ${e.toString()}');
    }
  }

  @override
  Future<List<WalletModel>> getUserWallets() async {
    try {
      final user = supabaseClient.auth.currentUser;
      if (user == null) {
        throw app_exceptions.AuthException(message: 'User not authenticated');
      }

      final response = await supabaseClient
          .from('wallets')
          .select('''
            *,
            wallet_members!inner(*),
            wallet_settings(*)
          ''')
          .eq('wallet_members.user_id', user.id)
          .order('created_at', ascending: false);

      return response.map((json) => WalletModel.fromJson(json)).toList();
    } on PostgrestException catch (e) {
      throw app_exceptions.DatabaseException(
          message: e.message, code: int.tryParse(e.code ?? ''));
    } catch (e) {
      throw app_exceptions.ServerException(
        message: 'Failed to get user wallets: ${e.toString()}',
      );
    }
  }

  @override
  Future<WalletModel> updateWallet(
    String walletId,
    CreateWalletRequest request,
  ) async {
    try {
      final updateData = {
        'name': request.name,
        'description': request.description,
        'goal_amount': request.goalAmount,
        'deadline': request.deadline?.toIso8601String(),
        'image_url': request.imageUrl,
        'updated_at': DateTime.now().toIso8601String(),
      };

      await supabaseClient
          .from('wallets')
          .update(updateData)
          .eq('id', walletId);

      return await getWallet(walletId);
    } on PostgrestException catch (e) {
      throw app_exceptions.DatabaseException(
          message: e.message, code: int.tryParse(e.code ?? ''));
    } catch (e) {
      throw app_exceptions.ServerException(
        message: 'Failed to update wallet: ${e.toString()}',
      );
    }
  }

  @override
  Future<void> deleteWallet(String walletId) async {
    try {
      await supabaseClient.from('wallets').delete().eq('id', walletId);
    } on PostgrestException catch (e) {
      throw app_exceptions.DatabaseException(
          message: e.message, code: int.tryParse(e.code ?? ''));
    } catch (e) {
      throw app_exceptions.ServerException(
        message: 'Failed to delete wallet: ${e.toString()}',
      );
    }
  }

  @override
  Future<WalletModel> joinWallet(String inviteCode) async {
    try {
      final user = supabaseClient.auth.currentUser;
      if (user == null) {
        throw app_exceptions.AuthException(message: 'User not authenticated');
      }

      // Find the invitation
      final invitationResponse = await supabaseClient
          .from('invitations')
          .select('*, wallets(*)')
          .eq('code', inviteCode)
          .eq('status', InvitationStatus.pending.name)
          .single();

      final invitation = InvitationModel.fromJson(invitationResponse);

      if (DateTime.now().isAfter(invitation.expiresAt)) {
        throw app_exceptions.ServerException(message: 'Invitation has expired');
      }

      // Add user as member
      final memberData = {
        'user_id': user.id,
        'wallet_id': invitation.walletId,
        'role': MemberRole.member.name,
        'contributed_amount': 0.0,
        'spending_limit': 0.0,
        'can_spend': true,
        'can_invite': false,
        'joined_at': DateTime.now().toIso8601String(),
        'invited_by': invitation.invitedBy,
      };

      await supabaseClient.from('wallet_members').insert(memberData);

      // Update invitation status
      await supabaseClient.from('invitations').update({
        'status': InvitationStatus.accepted.name,
        'accepted_at': DateTime.now().toIso8601String(),
        'accepted_by': user.id,
      }).eq('id', invitation.id);

      return await getWallet(invitation.walletId);
    } on PostgrestException catch (e) {
      throw app_exceptions.DatabaseException(
          message: e.message, code: int.tryParse(e.code ?? ''));
    } catch (e) {
      throw app_exceptions.ServerException(
          message: 'Failed to join wallet: ${e.toString()}');
    }
  }

  @override
  Future<WalletModel> acceptInvitation(String inviteCode) async {
    return await joinWallet(inviteCode);
  }

  // Member Management Methods
  @override
  Future<List<WalletMemberModel>> getWalletMembers(String walletId) async {
    try {
      final response = await supabaseClient
          .from('wallet_members')
          .select('*')
          .eq('wallet_id', walletId);

      return response.map((json) => WalletMemberModel.fromJson(json)).toList();
    } on PostgrestException catch (e) {
      throw app_exceptions.DatabaseException(
          message: e.message, code: int.tryParse(e.code ?? ''));
    } catch (e) {
      throw app_exceptions.ServerException(
        message: 'Failed to get wallet members: ${e.toString()}',
      );
    }
  }

  @override
  Future<WalletMemberModel> updateMemberRole(
    String walletId,
    String userId,
    MemberRole role,
  ) async {
    try {
      final response = await supabaseClient
          .from('wallet_members')
          .update({'role': role.name})
          .eq('wallet_id', walletId)
          .eq('user_id', userId)
          .select()
          .single();

      return WalletMemberModel.fromJson(response);
    } on PostgrestException catch (e) {
      throw app_exceptions.DatabaseException(
          message: e.message, code: int.tryParse(e.code ?? ''));
    } catch (e) {
      throw app_exceptions.ServerException(
        message: 'Failed to update member role: ${e.toString()}',
      );
    }
  }

  @override
  Future<WalletMemberModel> updateMemberPermissions(
    String walletId,
    String userId,
    UpdateMemberPermissionsRequest request,
  ) async {
    try {
      final updateData = <String, dynamic>{};
      if (request.spendingLimit != null)
        updateData['spending_limit'] = request.spendingLimit;
      if (request.canSpend != null) updateData['can_spend'] = request.canSpend;
      if (request.canInvite != null)
        updateData['can_invite'] = request.canInvite;

      final response = await supabaseClient
          .from('wallet_members')
          .update(updateData)
          .eq('wallet_id', walletId)
          .eq('user_id', userId)
          .select()
          .single();

      return WalletMemberModel.fromJson(response);
    } on PostgrestException catch (e) {
      throw app_exceptions.DatabaseException(
          message: e.message, code: int.tryParse(e.code ?? ''));
    } catch (e) {
      throw app_exceptions.ServerException(
        message: 'Failed to update member permissions: ${e.toString()}',
      );
    }
  }

  @override
  Future<void> removeMember(String walletId, String userId) async {
    try {
      await supabaseClient
          .from('wallet_members')
          .delete()
          .eq('wallet_id', walletId)
          .eq('user_id', userId);
    } on PostgrestException catch (e) {
      throw app_exceptions.DatabaseException(
          message: e.message, code: int.tryParse(e.code ?? ''));
    } catch (e) {
      throw app_exceptions.ServerException(
        message: 'Failed to remove member: ${e.toString()}',
      );
    }
  }

  // Invitation Management Methods
  @override
  Future<InvitationModel> createInvitation(
    CreateInvitationRequest request,
  ) async {
    try {
      final user = supabaseClient.auth.currentUser;
      if (user == null) {
        throw app_exceptions.AuthException(message: 'User not authenticated');
      }

      final invitationData = {
        'wallet_id': request.walletId,
        'invited_by': user.id,
        'email': request.email,
        'code': uuid.v4(),
        'status': InvitationStatus.pending.name,
        'expires_at':
            (request.expiresAt ?? DateTime.now().add(const Duration(days: 7)))
                .toIso8601String(),
        'created_at': DateTime.now().toIso8601String(),
      };

      final response = await supabaseClient
          .from('invitations')
          .insert(invitationData)
          .select()
          .single();

      return InvitationModel.fromJson(response);
    } on PostgrestException catch (e) {
      throw app_exceptions.DatabaseException(
          message: e.message, code: int.tryParse(e.code ?? ''));
    } catch (e) {
      throw app_exceptions.ServerException(
        message: 'Failed to create invitation: ${e.toString()}',
      );
    }
  }

  @override
  Future<List<InvitationModel>> getWalletInvitations(String walletId) async {
    try {
      final response = await supabaseClient
          .from('invitations')
          .select('*')
          .eq('wallet_id', walletId)
          .order('created_at', ascending: false);

      return response.map((json) => InvitationModel.fromJson(json)).toList();
    } on PostgrestException catch (e) {
      throw app_exceptions.DatabaseException(
          message: e.message, code: int.tryParse(e.code ?? ''));
    } catch (e) {
      throw app_exceptions.ServerException(
        message: 'Failed to get wallet invitations: ${e.toString()}',
      );
    }
  }

  @override
  Future<void> cancelInvitation(String invitationId) async {
    try {
      await supabaseClient.from('invitations').update(
          {'status': InvitationStatus.cancelled.name}).eq('id', invitationId);
    } on PostgrestException catch (e) {
      throw app_exceptions.DatabaseException(
          message: e.message, code: int.tryParse(e.code ?? ''));
    } catch (e) {
      throw app_exceptions.ServerException(
        message: 'Failed to cancel invitation: ${e.toString()}',
      );
    }
  }

  @override
  Future<WalletSettingsModel> updateWalletSettings(
    String walletId,
    WalletSettings settings,
  ) async {
    try {
      final response = await supabaseClient
          .from('wallet_settings')
          .update(
            WalletSettingsModel(
              requireApprovalForSpending: settings.requireApprovalForSpending,
              approvalThreshold: settings.approvalThreshold,
              allowMemberInvites: settings.allowMemberInvites,
              allowMemberSpending: settings.allowMemberSpending,
              notifyOnTransactions: settings.notifyOnTransactions,
              notifyOnContributions: settings.notifyOnContributions,
              notifyOnInvitations: settings.notifyOnInvitations,
              autoLockOnGoalReached: settings.autoLockOnGoalReached,
              autoLockOnDeadline: settings.autoLockOnDeadline,
            ).toJson(),
          )
          .eq('wallet_id', walletId)
          .select()
          .single();

      return WalletSettingsModel.fromJson(response);
    } on PostgrestException catch (e) {
      throw app_exceptions.DatabaseException(
          message: e.message, code: int.tryParse(e.code ?? ''));
    } catch (e) {
      throw app_exceptions.ServerException(
        message: 'Failed to update wallet settings: ${e.toString()}',
      );
    }
  }
}
